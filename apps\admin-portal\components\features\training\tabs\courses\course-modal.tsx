import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { LazySelectV2 } from "@/components/ui/lazy-select/lazy-select-v2";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Course } from "@/lib/apis/training-modules";
import { capitalize } from "@/lib/utils";

import {
  useAddCourse,
  useUpdateCourse,
} from "../../hooks/use-training-module-mutations";
import { useInfiniteModules } from "../../hooks/use-training-module-queries";

export const COURSE_STATUSES = ["draft", "published", "archived"] as const;

const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  status: z.enum(COURSE_STATUSES, {
    errorMap: () => ({
      message: "Status is required",
    }),
  }),
  order: z.coerce
    .number({
      invalid_type_error: "Order must be a number",
    })
    .optional(),
  moduleId: z
    .string({
      required_error: "Module is required",
      invalid_type_error: "Module is required",
    })
    .min(1, "Module is required"),
  externalId: z.string().optional(),
  description: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedCourse: Course | null;
};

export const CourseModal = function ({
  isOpen,
  onClose,
  selectedCourse,
}: Props) {
  const courseId = useParams().id as string;
  const { mutateAsync: addCourse, isPending: isAdding } = useAddCourse();
  const { mutateAsync: updateCourse, isPending: isUpdating } =
    useUpdateCourse(courseId);

  const isEditing = !!selectedCourse;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing ? await updateCourse(data) : await addCourse(data);
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? "Edit" : "Add"} Course`}
    >
      <Form
        defaultValues={{
          name: selectedCourse?.name || "",
          description: selectedCourse?.description || "",
          status: selectedCourse?.status || "",
          order: selectedCourse?.order,
          moduleId: selectedCourse?.moduleId || "",
          externalId: selectedCourse?.externalId || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid gap-4 sm:grid-cols-2 sm:gap-6">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <InputField
              id="name"
              name="name"
              placeholder="Enter course name..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              id="status"
              name="status"
              options={COURSE_STATUSES.map((status) => ({
                label: capitalize(status),
                value: status,
              }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="moduleId">Module</Label>
            <LazySelectV2
              name="moduleId"
              id="moduleId"
              searchPlaceholder="Search module..."
              useInfiniteQuery={useInfiniteModules}
              getOptionLabel={(module) => module.name}
              getOptionValue={(module) => module.id}
              placeholder="Select module"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="order">Order</Label>
            <InputNumber id="order" name="order" placeholder="Enter order..." />
          </div>

          <div className="space-y-2 sm:col-span-2">
            <Label htmlFor="externalId">External Id</Label>
            <InputField
              id="externalId"
              name="externalId"
              placeholder="Enter external id..."
            />
          </div>

          <div className="space-y-2 sm:col-span-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
            />
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            disabled={isAdding || isUpdating}
            isLoading={isAdding || isUpdating}
            color="blue"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
