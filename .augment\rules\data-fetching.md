---
type: "always_apply"
---

# Data Fetching Standards

This document establishes standardized patterns for data fetching across the admin-portal application, based on the training module implementation as the reference standard.

## Core Libraries

- **React Query (@tanstack/react-query)** - Used for all data fetching, caching, and mutations
- **nuqs** - Used for URL state management of filters, pagination, and sorting
- **Axios** - HTTP client wrapped in custom HttpClient class

## API Structure Standards

### Folder Organization

API modules should follow this standardized structure:

```
lib/apis/{feature-name}/
├── index.ts          # Exports API instance and types
├── {feature-name}.ts # API class implementation
└── types.ts          # TypeScript type definitions
```

**Example from training modules:**

```
lib/apis/training-modules/
├── index.ts
├── training-modules.ts
└── types.ts
```

### API Class Implementation

All API classes should extend `BaseApi` and follow these patterns:

```typescript
// lib/apis/training-modules/training-modules.ts
import BaseApi from "../base";
import { MetadataParams } from "../types";
import { Course, CourseListResponse, AddCoursePayload } from "./types";

export class TrainingModulesApi extends BaseApi {
  constructor() {
    super("/training", true); // true enables v2 API
  }

  // List operations with optional metadata params
  public async getCourses(params?: MetadataParams) {
    return this.http.get<CourseListResponse>(`/courses`, params);
  }

  // Detail operations
  public async getCourse(id: string) {
    return this.http.get<Course>(`/courses/${id}`);
  }

  // Create operations
  public async addCourse(data: AddCoursePayload) {
    return this.http.post<Course>("/courses", data);
  }

  // Update operations
  public async updateCourse(payload: UpdateCoursePayload) {
    return this.http.put<Course>(`/courses/${payload.id}`, payload);
  }

  // Delete operations
  public async deleteCourse(id: string) {
    return this.http.delete(`/courses/${id}`);
  }
}
```

### API Module Exports

The `index.ts` file should export both the API instance and all types:

```typescript
// lib/apis/training-modules/index.ts
import { TrainingModulesApi } from "./training-modules";

export * from "./types";
export const trainingModules = new TrainingModulesApi();
```

### TypeScript Type Definitions

Types should be organized in `types.ts` with clear naming conventions:

```typescript
// lib/apis/training-modules/types.ts
import { ListBaseResponse } from "../types";

// Entity types
export type Course = {
  id: string;
  name: string;
  description: string;
  status: CourseStatus;
  createdDate: string;
  lastUpdatedDate: string;
};

// Payload types for mutations
export type AddCoursePayload = {
  name: string;
  description?: string;
  status: CourseStatus;
  moduleId: string;
};

export type UpdateCoursePayload = AddCoursePayload & {
  id: string;
};

// Response types
export type CourseListResponse = ListBaseResponse<Course>;
```

### Error Handling in APIs

The `BaseApi` class handles errors through the `HttpClient`, which:

- Automatically adds authentication headers
- Handles response errors consistently
- Provides retry logic with exponential backoff
- Refreshes tokens automatically on auth failures

## Query Key Management

### Hierarchical Query Key Structure

Query keys should be organized hierarchically using a factory pattern for consistency and cache invalidation:

```typescript
// components/features/training/hooks/use-training-module-queries.ts
export const trainingModuleKeys = {
  // Root level - for invalidating all training module queries
  all: () => ["training-modules"] as const,

  // Feature level - for invalidating all lists of a specific type
  allModuleLists: () => [...trainingModuleKeys.all(), "modules"] as const,
  // Parameterized lists - include params for specific cache entries
  moduleList: (params?: MetadataParams) =>
    [...trainingModuleKeys.allModuleLists(), params] as const,
  // Detail queries
  allCourseDetails: () => [...trainingModuleKeys.all(), "detail"] as const,
  courseDetail: (id: string) =>
    [...trainingModuleKeys.allCourseDetails(), id] as const,
};
```

### Query Key Naming Conventions

1. **Root keys** - Use feature name: `["training-modules"]`
2. **List keys** - Add resource type: `["training-modules", "courses"]` or `["roles", "list"]` (depend on cases)
3. **Parameterized keys** - Include params object: `["training-modules", "courses", params]`
4. **Detail keys** - Add "detail" and ID: `["training-modules", "detail", id]`
5. **Nested resources** - Build hierarchy: `["training-modules", "chapters", courseId, params]`

### Cache Invalidation Patterns

Use hierarchical keys for targeted cache invalidation:

```typescript
// Invalidate all training module queries
queryClient.invalidateQueries({ queryKey: trainingModuleKeys.all() });

// Invalidate all course lists (but not details)
queryClient.invalidateQueries({
  queryKey: trainingModuleKeys.allCourseLists(),
});

// Invalidate specific course detail
queryClient.invalidateQueries({
  queryKey: trainingModuleKeys.courseDetail(id),
});
```

### Query Key Best Practices

1. **Always use `as const`** - Ensures type safety and prevents mutations
2. **Include all parameters** - Any parameter that affects the query result should be in the key
3. **Use factory functions** - Centralize key generation for consistency
4. **Build hierarchically** - Use spread operator to build on parent keys
5. **Export key factories** - Make them available for mutations and invalidation

## React Query Hook Patterns

### Standard useQuery Hook Structure

Query hooks should consume filter state internally and follow this pattern:

```typescript
// components/features/training/hooks/use-training-module-queries.ts
export const useCourses = () => {
  // Consume URL state internally via custom hooks
  const { page, take } = usePagination();
  const { orderBy, orderDirection } = useSort();

  // Build params object
  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
  };

  return useQuery({
    queryKey: trainingModuleKeys.courseList(params),
    queryFn: () => api.trainingModules.getCourses(params),
    placeholderData: (prev) => prev, // Keep previous data during refetch
  });
};
```

### Detail Query Pattern

For single resource queries with conditional fetching:

```typescript
export const useCourse = (id: string) => {
  return useQuery({
    queryKey: trainingModuleKeys.courseDetail(id),
    queryFn: () => api.trainingModules.getCourse(id),
    placeholderData: (prev) => prev,
  });
};

// With conditional fetching
export const useChapterProgressesByEnrollmentId = (id?: string) => {
  return useQuery({
    queryKey: id
      ? trainingModuleKeys.chapterProgressListByEnrollmentId(id)
      : [],
    queryFn: id
      ? () => api.trainingModules.getChapterProgressesByEnrollmentId(id)
      : skipToken,
  });
};
```

### Mutation Hook Pattern

Mutations should include error handling and cache invalidation:

```typescript
// components/features/training/hooks/use-training-module-mutations.ts
export const useAddCourse = () =>
  useMutation({
    mutationFn: (payload: AddCoursePayload) =>
      api.trainingModules.addCourse(payload),
    onError: (err) => toast.error(err?.message || "Fail to add course"),
    onSettled: (_, err) => !err && toast.success("Add course successfully"),
    meta: {
      awaits: trainingModuleKeys.allCourseLists(), // For cache invalidation
    },
  });

// Update mutation with multiple invalidation targets
export const useUpdateCourse = (id: string) =>
  useMutation({
    mutationFn: (payload: AddCoursePayload) =>
      api.trainingModules.updateCourse({ ...payload, id }),
    onError: (err) => toast.error(err?.message || "Fail to update course"),
    onSettled: (_, err) => !err && toast.success("Update course successfully"),
    meta: {
      awaits: [
        trainingModuleKeys.allCourseLists(),
        trainingModuleKeys.courseDetail(id),
      ],
    },
  });
```

### Query Configuration Best Practices

1. **placeholderData: (prev) => prev** - Keep previous data during refetch for smooth UX (only apply if the query contains params)

## URL State Management

### Using useQueryState from nuqs directly in the custom query hooks

For features with fewer than 4 total filters (excluding usePagination, useSearch, and useSort)

```typescript
// components/features/roles/hooks/use-roles-query.ts
import { parseAsString, useQueryState } from "nuqs";
import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";

export const useCourses = () => {
  // Consume URL state internally via custom hooks
  const { page, take } = usePagination();
  const { orderBy, orderDirection } = useSort();
  const [folderId, setFolderId] = useQueryState("folderId", parseAsString);
  // Build params object
  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      folderId,
    },
  };

  return useQuery({
    queryKey: trainingModuleKeys.courseList(params),
    queryFn: () => api.trainingModules.getCourses(params),
    placeholderData: (prev) => prev, // Keep previous data during refetch
  });
};
```

### Complex Filter Hook Pattern

For features with multiple filter types:

```typescript
// components/features/sites/site-detail/local-study/tabs/isf/hooks/use-filter-documents.ts
export const useFilterDocuments = () => {
  const siteId = useParams().id as string;
  const studyId = useParams().studyId as string;

  // Standard pagination and search
  const { search, changeSearch } = useSearch();
  const { page, take, goToPage } = usePagination();
  const { orderBy, orderDirection, changeSort } = useSort();

  // Custom filters with nuqs
  const [folderId, setFolderId] = useQueryState("folderId", parseAsString);
  const [extension, setExtension] = useQueryState("extension", parseAsString);
  const [fromCreatedDate, setFromCreatedDate] = useQueryState(
    "fromCreatedDate",
    parseAsString,
  );
  const [toCreatedDate, setToCreatedDate] = useQueryState(
    "toCreatedDate",
    parseAsString,
  );

  return {
    siteId,
    studyId,
    search,
    page,
    take,
    orderBy,
    orderDirection,
    changeSort,
    folderId,
    setFolderId,
    extension,
    setExtension,
    fromCreatedDate,
    setFromCreatedDate,
    toCreatedDate,
    setToCreatedDate,
    changeSearch,
    goToPage,
  };
};
```

### Built-in URL State Hooks

Use these standardized hooks for common URL state:

```typescript
// hooks/use-pagination.ts
export function usePagination() {
  const [page, setPage] = useQueryState("page", parseAsInteger.withDefault(1));
  const [take, setTake] = useQueryState(
    "take",
    parseAsInteger.withDefault(100),
  );

  const goToPage = useCallback(
    (newPage: number) => setPage(newPage),
    [setPage],
  );
  const changePageSize = useCallback(
    (newTake: number) => {
      setTake(newTake);
      setPage(1);
    },
    [setPage, setTake],
  );

  return { page, take, goToPage, changePageSize };
}

// hooks/use-sort.ts
export const useSort = () => {
  const [orderBy, setOrderBy] = useQueryState("orderBy", parseAsString);
  const [orderDirection, setOrderDirection] = useQueryState(
    "orderDirection",
    parseAsString,
  );

  const changeSort = useCallback(
    (sortBy?: string, sortOrder?: SortDirection) => {
      setOrderBy(sortBy || null);
      setOrderDirection(sortOrder || null);
    },
    [setOrderBy, setOrderDirection],
  );

  return { orderBy, orderDirection, changeSort };
};
```

### Self-Contained Component Pattern

Components should consume filters internally rather than accepting them as props:

```typescript
// ❌ Avoid prop drilling
const MyComponent = ({ filters }: { filters: FilterState }) => {
  const { data } = useQuery({
    queryKey: ["data", filters],
    queryFn: () => api.getData(filters),
  });
};

// ✅ Preferred: Self-contained with internal filter consumption
const MyComponent = () => {
  const filters = useMyFilters(); // Reads from URL internally
  const { data } = useQuery({
    queryKey: ["data", filters],
    queryFn: () => api.getData(filters),
  });
};
```

## Folder Organization Standards

### Feature-Based Hook Organization

Organize data fetching hooks within feature directories:

```
components/features/{feature-name}/
├── hooks/
   ├── use-{feature}-queries.ts    # Query hooks
   ├── use-{feature}-mutations.ts  # Mutation hooks
   └── use-{feature}-filters.ts    # Filter hooks (optional)

```

**Example from training modules:**

```
components/features/training/
├── hooks/
│   ├── use-training-module-queries.ts
│   └── use-training-module-mutations.ts
└── ...
```

### Shared Hook Organization

Place commonly used hooks in the root hooks directory:

```
hooks/
├── use-pagination.ts    # URL-based pagination
├── use-search.ts        # URL-based search
├── use-sort.ts          # URL-based sorting
└── queries/             # Shared query hooks
    └── use-infinite-prompt-variables.ts
```

### Import/Export Patterns

1. **Import API from centralized location**:

```typescript
import api from "@/lib/apis";
```

2. **Import types from API modules**:

```typescript
import { AddCoursePayload, Course } from "@/lib/apis/training-modules";
```

## Naming Conventions

### File Naming

- Query hooks: `use-{feature}-queries.ts`
- Mutation hooks: `use-{feature}-mutations.ts`
- Filter hooks: `use-{feature}-filters.ts`
- API classes: `{feature}.ts`
- Types: `types.ts`

### Function Naming

- Query hooks: `use{EntityPlural}()`, `use{Entity}(id)`, `use{Entity}By{Criteria}()`
- Mutation hooks: `useAdd{Entity}()`, `useUpdate{Entity}()`, `useDelete{Entity}()`
- Filter hooks: `use{Entity}Filters()` or `useFilter{Entity}()`
- Query keys: `{entity}Keys`

## Best Practices Summary

1. **Self-contained components** - Consume filters internally via custom hooks
2. **Hierarchical query keys** - Use factory pattern for consistent cache management
3. **URL state management** - Use nuqs for all filter, pagination, and sorting state
4. **Placeholder data** - Keep previous data during refetches for smooth UX
5. **Error handling** - Include toast notifications and proper error states
6. **Cache invalidation** - Use meta.awaits or explicit invalidation in mutations
7. **TypeScript types** - Define comprehensive types for all API interactions
8. **Consistent patterns** - Follow established patterns across all features

This documentation serves as the definitive guide for implementing data fetching patterns that ensure consistency, maintainability, and optimal performance across the admin-portal application.

