# Data Fetching

## Core Libraries

- React Query (@tanstack/react-query) is used for all data fetching, caching, and mutations.
- nuqs is used for synchronizing pagination and sorting state with the URL.

## Query Patterns

- useQuery is used for fetching single resources or lists.
- useInfiniteQuery is used for paginated/infinite loading lists.
- useMutation is used for data-changing operations (create, update, delete).

## Pagination

- usePagination provides page and take (page size) state, synchronized with the URL.
- Pagination params are passed to API calls and included in query keys.

## Sorting

- useSort provides orderBy and orderDirection state, synchronized with the URL.
- Sorting params are passed to API calls and included in query keys.

## Query Keys

- Query keys are structured and hierarchical, including all relevant params (filters, pagination, sorting).
- Query key constants are exported for reuse.

## Infinite Queries

- useInfiniteQuery is used for lists with pagination, with getNextPageParam for loading more pages.
- initialPageParam is set to 1.

## Mutations

- useMutation is used for all data-changing operations.
- useOptimisticMutation provides optimistic UI updates, with rollback on error and cache invalidation on settle.
- onSuccess and onError handlers are used for side effects and error handling.
- queryClient.invalidateQueries is used to refresh relevant queries after mutations.

## Placeholder Data

- placeholderData is used to keep previous data during refetches for a smoother UX.

## Debouncing

- useDebounce is used for debouncing search/filter input before triggering queries.

## File Uploads

- useUppy integrates Uppy file uploads with React Query for cache invalidation after upload.
- queryInvalidations option is used to specify which queries to invalidate after upload.

## Example Usage

### Fetching a List

```
const { data, isLoading } = useQuery({
  queryKey: ["users", params],
  queryFn: () => api.users.list(params),
});
```

### Infinite Query

```
const { data, fetchNextPage } = useInfiniteQuery({
  queryKey: ["users", search],
  queryFn: ({ pageParam = 1 }) => api.users.list({ page: pageParam, take: 50, filter: { name: search } }),
  getNextPageParam: (lastPage) => lastPage.metadata.currentPage < lastPage.metadata.totalPages ? lastPage.metadata.currentPage + 1 : undefined,
  initialPageParam: 1,
});
```

### Mutation with Optimistic Update

```
const mutation = useOptimisticMutation({
  mutationFn: api.update,
  queryKey: ["resource", id],
  updater: (old, variables) => ({ ...old, ...variables }),
  invalidates: [["resource", id]],
});
```

### File Upload with Cache Invalidation

```
const { uppy, startUpload } = useUppy({
  queryInvalidations: [["documents"]],
}, {
  onUpload: async (files, meta) => { /* upload logic */ },
});
```
